# AI提示词模板集合
## 基于调研数据的用户场景描述与宣传资料生成

## 使用说明

这些模板专门用于基于**atypica.ai调研结果**进行用户场景深度描述，最终目的是为**生成宣传资料做准备**，支持A/B测试验证：

1. **前置条件**：已完成atypica.ai调研，获得用户画像和需求数据
2. **核心作用**：将调研数据转化为生动的用户场景和故事
3. **最终目标**：为产品营销文案、广告创意、宣传视频等提供素材
4. **验证机制**：通过A/B测试验证不同场景描述的营销效果

## 场景构建核心理念

基于梁宁的场景理论："场景=场+景"
- **场**：时间+空间，用户可以停留和消费的环境
- **景**：情景+互动，触发用户情绪并裹挟用户意见的元素
- **关键**：只有能触发用户情绪的场景才是真正的流量入口

## 用户画像深度挖掘模板

### 1. 基于调研数据的角色立体化模板

```
你是专业的用户洞察分析师。基于以下atypica.ai调研数据：
[插入完整调研报告内容]

请为目标用户群体创建立体化角色画像，用于后续营销内容创作：

**任务要求**：
为调研中出现频率最高的用户类型创建3个具体角色，每个角色包含：

**基础信息模块**：
- 姓名、年龄、职业、收入、居住地
- 教育背景、家庭状况、生活节奏

**深层心理模块**：
- 核心价值观和人生追求
- 对成功/失败的定义
- 面临的主要生活压力
- 内心深层的恐惧和渴望

**行为特征模块**：
- 典型的一天时间分配
- 消费决策的心理过程
- 信息获取渠道和习惯
- 社交行为模式和圈层

**痛点情绪模块**：
- 具体痛点在什么情况下被触发
- 痛点带来的情绪强度（0-10分）
- 痛点持续的时间和频率
- 解决痛点的紧迫程度

**解决方案期望**：
- 理想解决方案的具体特征
- 愿意为解决方案付出的代价
- 选择方案时的核心判断标准
- 对品牌的信任建立过程

**输出格式**：为每个角色写一个300字的生动人物小传，让人能"看见"这个人。
```

## 场景化故事构建模板

### 2. 痛点触发场景深度描写模板

```
基于atypica.ai调研发现的用户痛点，请构建触发场景的深度描写：

**调研痛点数据**：[插入调研中的具体痛点]
**目标用户画像**：[插入用户角色]

**场景构建要求**：
为每个主要痛点创建一个"痛点爆发时刻"的场景描写，包含：

**时空设定（场）**：
- 具体时间：周几、几点、什么节点
- 具体空间：在哪里、什么环境、周围是什么
- 空间氛围：嘈杂/安静、明亮/昏暗、温暖/冷漠

**情绪触发（景）**：
- 痛点被什么事件触发
- 用户的即时情绪反应
- 内心的具体感受和想法
- 身体的生理反应

**行为表现**：
- 用户的具体动作和表情
- 与环境/他人的互动
- 寻求解决方案的尝试
- 挫败感的具体表现

**故事化表达**：
用小说般的细腻笔触，写一个500字的场景片段，让读者能"感同身受"这个痛点。

**营销价值提示**：
- 这个场景最适合在什么营销渠道使用？
- 什么样的产品介绍能在此时打动用户？
- 如何在广告中还原这个场景的情绪？
```

### 3. 产品使用的理想场景构建模板

```
基于atypica.ai调研中用户对理想解决方案的期望，构建产品使用的美好场景：

**调研期望数据**：[插入用户对理想解决方案的描述]
**产品功能特点**：[插入产品核心功能]

**理想场景构建**：

**Before状态描述**：
- 用户使用产品前的困扰状态
- 具体的时间地点和情境
- 用户的焦虑情绪和无助感
- 之前尝试过的失败解决方案

**After状态描述**：
- 使用产品后的愉悦状态
- 问题被解决的具体表现
- 用户的喜悦情绪和成就感
- 生活品质的具体提升

**转折时刻刻画**：
- 用户决定尝试产品的契机
- 第一次使用时的心理活动
- 效果超出预期的惊喜时刻
- 从尝试到信任的心理转变

**故事化场景**：
写一个800字的"一天生活对比"故事，展现用户使用产品前后的生活变化。

**宣传素材提示**：
- 这个故事适合制作什么类型的宣传内容？
- 核心情感点在哪里，如何放大？
- 什么样的视觉或音频元素能增强感染力？
```

## 情感触发文案模板

### 4. 基于场景的情感共鸣文案模板

```
基于前面构建的用户场景，创作能触发目标用户情感共鸣的营销文案：

**场景输入**：[插入前面创建的用户痛点场景]
**产品定位**：[插入产品的核心价值主张]

**文案创作框架**：

**痛点共鸣部分**：
- 用一句话点出用户的核心困扰
- 描绘困扰带来的具体影响
- 让用户产生"说中了我的心声"的感觉

**解决方案引入**：
- 自然过渡到产品介绍
- 重点强调产品如何针对性解决痛点
- 用具体功能说话，避免空洞承诺

**美好结果描绘**：
- 描绘使用产品后的理想状态
- 具体化改变的表现
- 让用户能想象到自己的美好未来

**行动召唤**：
- 降低用户尝试的心理门槛
- 提供具体的下一步行动指引
- 营造紧迫感但不过于aggressive

**输出要求**：
1. 创作3个不同风格的文案版本（理性型、感性型、故事型）
2. 每个版本包含标题和正文
3. 标明每个版本适合的投放渠道和目标人群
4. 提供A/B测试的变量建议
```

### 5. 多渠道场景化内容改写模板

```
基于核心用户场景，为不同营销渠道改写场景化内容：

**原始场景故事**：[插入核心用户场景]
**核心信息**：[插入要传达的产品信息]

**渠道适配要求**：

**抖音/短视频版本**：
- 时长：15秒、30秒、60秒三个版本
- 脚本：分镜头描述，突出视觉冲击
- 关键元素：开头3秒抓眼球，结尾强化记忆点
- 情绪节奏：快速切换，情绪起伏明显

**小红书/种草版本**：
- 内容：真实体验分享风格
- 结构：问题-尝试-效果-推荐
- 风格：生活化、亲近感、可信度高
- 互动：预设用户可能的评论和回复

**微信公众号/深度版本**：
- 内容：深度分析问题和解决方案
- 结构：引入-分析-案例-总结
- 风格：专业性与故事性结合
- 长度：2000-3000字，有理有据

**朋友圈/社交版本**：
- 内容：个人化体验分享
- 长度：控制在200字以内
- 风格：自然、非广告化
- 配图：建议的图片类型和数量

**电梯/线下版本**：
- 时长：30秒电梯演讲
- 重点：最核心的痛点和价值
- 记忆点：一句话让人记住
- 跟进：如何获得更多信息

**输出格式**：每个渠道提供完整的内容方案和投放建议。
```

## 视觉化场景设计模板

### 6. 场景视觉化创意指导模板

```
基于用户场景故事，为视觉内容创作提供详细指导：

**核心场景**：[插入要视觉化的用户场景]
**传达信息**：[插入核心营销信息]

**视觉化框架**：

**场景还原设计**：
- 环境布景：具体的场所、时间、天气、光线
- 人物造型：目标用户的外观、服装、表情、动作
- 道具设置：周围的物品、细节元素
- 色彩基调：主色调和情绪氛围

**情绪传达设计**：
- 痛点表现：如何通过视觉表现用户的困扰
- 解决时刻：如何展现问题被解决的瞬间
- 情绪对比：Before/After的视觉对比
- 细节强化：放大哪些细节来强化情感

**产品植入设计**：
- 出现时机：产品在什么时候、以什么方式出现
- 展示角度：产品的哪些特征需要重点突出
- 使用场景：如何自然地展示产品使用过程
- 结果展现：如何可视化产品带来的改变

**不同形式指导**：

**平面广告/海报**：
- 构图建议：主体位置、视觉引导路径
- 文字配合：文案与视觉的结合点
- 留白运用：如何营造呼吸感

**短视频脚本**：
- 镜头切换：每个镜头的持续时间和切换节奏
- 音效配合：背景音乐和音效的情绪配合
- 字幕设计：关键信息的字幕呈现

**H5/交互设计**：
- 交互流程：用户操作路径
- 动效设计：关键转场和强化效果
- 响应式适配：不同设备的呈现差异

**输出要求**：
提供详细的视觉创意brief，包含具体的制作指导和效果期望。
```

## A/B测试优化模板

### 7. 基于场景的A/B测试设计模板

```
基于不同用户场景，设计系统性的A/B测试方案：

**测试背景**：
- 目标用户群体：[基于调研数据]
- 核心场景：[插入要测试的场景]
- 营销目标：[认知/转化/留存等]

**变量设计矩阵**：

**场景变量测试**：
- A组：工作日压力场景
- B组：周末休闲场景
- C组：社交展示场景
- 测试指标：点击率、停留时间、转化率

**情绪强度测试**：
- A组：高情绪强度（强烈痛点描述）
- B组：中等情绪强度（温和问题提出）
- C组：低情绪强度（理性功能介绍）
- 测试指标：情绪反应、分享率、购买意愿

**角色共鸣测试**：
- A组：职场精英人设
- B组：普通白领人设
- C组：创业者人设
- 测试指标：认同感、品牌好感度

**解决方案呈现**：
- A组：功能特性导向
- B组：使用体验导向
- C组：结果效果导向
- 测试指标：理解度、可信度、购买意向

**测试执行计划**：

**样本分配**：
- 总样本量计算依据
- 各组样本量分配比例
- 测试时长和观察周期

**数据收集**：
- 定量指标：点击、转化、停留等
- 定性反馈：评论、调研、访谈
- 行为数据：热力图、路径分析

**结果分析框架**：
- 统计显著性检验
- 商业价值评估
- 用户行为模式分析
- 后续优化建议

**输出要求**：
提供完整的测试执行手册和结果分析模板。
```

### 8. 场景效果优化迭代模板

```
基于A/B测试结果，持续优化场景描述和营销内容：

**测试结果输入**：[插入A/B测试数据和用户反馈]

**效果分析框架**：

**成功场景提炼**：
- 哪个场景版本效果最好？
- 成功的关键元素是什么？
- 用户反馈中的高频词汇
- 情绪触发的关键节点

**失败原因分析**：
- 低效场景的问题在哪里？
- 用户抵触的具体表现
- 与用户期望的差距
- 竞品对比中的劣势

**用户行为洞察**：
- 不同场景下的用户行为差异
- 转化路径的关键节点
- 流失原因和时机分析
- 复购/推荐行为的触发因素

**优化策略制定**：

**场景深化**：
- 成功场景的细节强化
- 增加更多真实性细节
- 提升情绪触发强度
- 优化叙事节奏

**多场景组合**：
- 不同场景的组合策略
- 针对不同用户群体的场景配比
- 营销漏斗各阶段的场景应用
- 季节性/时效性场景调整

**内容形式创新**：
- 新的表达方式尝试
- 多媒体形式的场景呈现
- 互动性内容的场景应用
- 用户生成内容的引导

**下轮测试设计**：
- 新的测试假设
- 更精细的变量控制
- 扩大测试范围
- 长期效果追踪

**输出要求**：
制定下一阶段的场景优化方案和测试计划。
```

## 最佳实践指南

### 场景构建技巧
1. **真实性优先**：基于真实调研数据，避免主观臆测
2. **情绪为王**：重点刻画能触发情绪的细节
3. **具体化表达**：用具体的时间、地点、人物、事件代替抽象描述
4. **多感官描述**：调动视觉、听觉、触觉等多重感官体验

### 内容创作要点
1. **用户视角**：站在用户角度，用用户的语言表达
2. **场景联结**：将产品功能与具体使用场景深度联结
3. **情绪引导**：从痛点触发到解决方案的情绪引导
4. **行动导向**：每个内容都有明确的用户行动指引

### A/B测试策略
1. **单变量控制**：每次只测试一个关键变量
2. **统计严格性**：确保样本量和测试时长的统计意义
3. **商业价值导向**：不只看数据表现，更看商业转化
4. **长期效果评估**：关注用户生命周期价值的影响

## 远航科技案例应用示例

**基于调研的场景构建**：
- **痛点场景**：张经理周三晚上8点刚到家，看着空荡荡的冰箱，想着明天的重要会议，既想吃健康的家常菜又没时间做饭的焦虑时刻
- **解决场景**：30分钟后，香喷喷的宫保鸡丁端上餐桌，张经理一边享受美食一边回顾今天的成就，内心充满满足感
- **测试变量**：工作压力vs健康需求，效率导向vs品质生活，功能介绍vs体验故事

这些模板将帮助你将atypica.ai的调研数据转化为生动的用户场景和高转化的营销内容。记住：好的营销内容不是产品介绍，而是用户故事；好的用户故事来自于深度的场景洞察。 