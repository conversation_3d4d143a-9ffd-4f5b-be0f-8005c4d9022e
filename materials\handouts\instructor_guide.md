# 讲师实施指南（90分钟版）

## 课前准备清单

### 技术准备
- [ ] 确保网络稳定，可访问ChatGPT、Claude等AI工具
- [ ] 准备备用网络方案（手机热点）
- [ ] 投影设备调试，确保屏幕分享清晰
- [ ] 准备白板和彩色马克笔（至少3种颜色）
- [ ] 准备场景设计画布模板（每人1份）

### 材料准备
- [ ] 学员名牌和便利贴（每人5张）
- [ ] 场景设计画布打印（每人1份）
- [ ] 计时器或手机计时App
- [ ] Before-After对比案例备份（防网络问题）

### 环境布置
- [ ] 两人一组的座位安排（便于快速配对）
- [ ] 准备"最佳提示词"展示墙
- [ ] 音响设备测试（提示音和背景音乐）
- [ ] 确保所有学员都能清楚看到大屏幕

---

## 第一部分：破冰与认知颠覆（20分钟）

### 开场：吃饭场景的启示（8分钟）

**讲师要点**：
- 快速建立亲和力，用生活化例子降低学习门槛
- 重点突出"同样产品，不同场景，完全不同消费"
- 为后续AI场景化应用做铺垫

**操作细节**：
1. **破冰互动**（2分钟）
   ```
   "大家好！开始之前先问个问题：今天早餐大家都怎么吃的？"
   ```
   - 快速收集2-3个不同的回答
   - 引导学员关注不同的用餐场景

2. **场景对比分析**（4分钟）
   - **早餐对比**：匆忙上班 vs 周末慢生活
   - **午餐对比**：独自用餐 vs 同事聚餐 vs 商务宴请
   - **关键提问**："同样是吃饭，为什么消费差这么大？"

3. **核心概念揭示**（2分钟）
   - 场景决定消费：时间+空间+情绪=完全不同的需求
   - 连接AI应用：提示词也需要"场景化"才能发挥最佳效果

**时间把控**：
- 严格控制8分钟，不允许超时
- 如果学员反应热烈，可适当延长1分钟
- 预留30秒过渡到下一环节

**注意事项**：
- 避免让讨论偏向具体的餐厅或美食
- 始终围绕"场景影响消费"这个核心
- 观察学员理解程度，及时调整节奏

### 震撼对比演示（10分钟）

**讲师要点**：
- 这是课程的核心高潮，必须制造强烈震撼
- 展示过程要有戏剧性，营造期待感
- 重点突出效果差异的巨大性

**操作细节**：
1. **设置悬念**（1分钟）
   ```
   "刚才说吃饭场景能改变消费，那AI场景能改变什么？接下来见证奇迹时刻！"
   ```

2. **Before演示**（3分钟）
   - 现场输入传统提示词：`请帮我写一个智能手表的产品介绍`
   - 让学员快速评分（1-10分）
   - 记录平均分在白板上
   - 点评：典型的产品说明书，缺乏购买冲动

3. **After演示**（5分钟）
   - 戏剧性宣布："现在让同一个AI聪明10倍！"
   - 输入场景化提示词（边输入边解释设计思路）
   - 展示结果，再次让学员评分
   - 对比两次评分，强调差异巨大

4. **效果总结**（1分钟）
   - 强调：同一个AI，不同场景，10倍效果差异
   - 为下一环节理论学习做铺垫

**演示技巧**：
- 语速适中，确保学员能跟上思路
- 适当使用夸张手势和语调
- 及时观察学员表情，抓住"Aha moment"

**备选方案**：
- 准备2个不同行业案例备用
- 如网络问题，使用预先准备的截图
- 关键是确保震撼效果，技术细节次要

### 理论框架速讲（2分钟）

**讲师要点**：
- 快速建立理论框架，不深入展开
- 重点是让学员理解公式的实用性
- 为后续实操练习提供工具指导

**操作要点**：
1. **公式展示**（1分钟）
   - 白板写出：场景 = 场（时间+空间）+ 景（情景+互动+情绪触发）
   - 快速解释：这就是刚才奇迹背后的科学

2. **应用说明**（1分钟）
   - 时间+空间：具体的使用情境
   - 情景+互动：角色设定和对话方式
   - 情绪触发：让AI产生"共情"效果

---

## 第二部分：场景化提示词核心方法（40分钟）

### 模块1：场景要素设计（15分钟）

**教学策略**：
- 理论简化，工具实用，立即可用
- 边讲边练，保持学员参与度
- 重点关注实际应用而非理论深度

**操作流程**：
1. **快速理论**（3分钟）
   - 时间维度：工作vs休息、紧急vs从容
   - 空间维度：办公室vs家庭、公开vs私密
   - 角色要素：身份、状态、需求、关系

2. **5W1H工具介绍**（2分钟）
   - 展示分析框架，强调实用性
   - 现场演示一个简单案例
   - 发放场景设计画布

3. **快速练习**（8分钟）
   - 学员两人一组，选择一个企业场景
   - 用5W1H方法快速分析（5分钟）
   - 2-3组快速分享结果（3分钟）

4. **要点总结**（2分钟）
   - 强调具体化、细节化的重要性
   - 为下一模块做过渡

**讲师巡回要点**：
- 每组停留1分钟，快速指导
- 重点检查是否理解工具用法
- 及时纠正过于宽泛的描述

### 模块2：情绪触发技巧（10分钟）

**教学重点**：
- 情绪触发的实用技巧
- 避免理论过深，重点在应用
- 强调自然性和真实性

**操作流程**：
1. **核心技巧介绍**（3分钟）
   - 角色代入法：让AI扮演专业角色
   - 情境模拟法：构建真实业务场景
   - 情绪词汇法：植入关键触发词

2. **情绪类型速览**（2分钟）
   - 焦虑触发：时间紧迫、资源稀缺
   - 兴奋触发：突破创新、超越预期
   - 信任触发：专业权威、历史案例
   - 共鸣触发：相似经历、共同痛点

3. **现场小练习**（5分钟）
   - 基于模块1的场景分析
   - 每组选择适合的情绪触发类型
   - 在提示词中加入情绪元素
   - 1-2组现场分享

**注意事项**：
- 强调情绪要自然，不能生硬植入
- 重点关注商业场景的适用性
- 及时给予正向反馈

### 模块3：提示词结构化设计（15分钟）

**教学目标**：
- 提供标准化模板
- 让学员掌握可复用的结构
- 现场完成完整提示词设计

**操作流程**：
1. **模板介绍**（3分钟）
   - 展示标准结构模板
   - 解释每个部分的作用
   - 强调模板的通用性

2. **填写演示**（2分钟）
   - 选择一个简单案例
   - 现场演示模板填写过程
   - 重点突出关键要点

3. **学员实操**（8分钟）
   - 基于前两个模块的成果
   - 使用模板完成完整提示词
   - 讲师巡回指导

4. **效果测试**（2分钟）
   - 1-2组现场测试AI效果
   - 快速点评和改进建议

**模板要点强调**：
- 场景设定要具体
- 情境描述要真实
- 任务要求要明确
- 输出要求要详细

---

## 第三部分：企业级场景实战（25分钟）

### 实战案例演示（10分钟）

**教学目标**：
- 展示高级场景设计技巧
- 让学员看到应用可能性
- 为PK环节做示范

**操作要点**：
1. **B2B销售案例**（5分钟）
   - 快速展示传统vs场景化邮件对比
   - 重点分析：客户画像、情感触发、价值提供
   - 强调开信率和回复率的差异

2. **团队管理案例**（5分钟）
   - 展示管理沟通的场景化设计
   - 重点分析：共情建立、问题探询、解决方案
   - 强调人文关怀和实际效果

**演示技巧**：
- 快节奏展示，重点突出差异
- 用具体数据说话（开信率、满意度等）
- 鼓励学员思考自己企业的应用

### 学员现场PK（15分钟）

**组织要点**：
- 营造竞争氛围，激发参与热情
- 确保每位学员都有展示机会
- 及时给予认可和建设性反馈

**操作流程**：
1. **PK规则说明**（2分钟）
   - 两人一组，选择相同业务场景
   - 分别设计传统版和场景化版本
   - 现场测试并展示结果差异

2. **实战PK**（8分钟）
   - 学员配对并选择场景（2分钟）
   - 分别设计提示词（4分钟）
   - 现场测试AI效果（2分钟）

3. **成果展示**（5分钟）
   - 3组展示Before-After对比（3分钟）
   - 全班投票选出最佳案例（1分钟）
   - 快速点评和成功要素总结（1分钟）

**点评要点**：
- 先肯定亮点，再提改进建议
- 重点关注场景设计的完整性
- 强调实际应用价值

**时间控制**：
- 严格按时间执行，不允许拖延
- 准备计时器，适时提醒
- 重点确保展示环节的质量

---

## 第四部分：建立持续实践体系（5分钟）

### 30天行动计划（3分钟）

**目标**：
- 给学员明确的后续指导
- 建立学习效果的延续机制
- 创造再次成功的机会

**操作要点**：
1. **计划框架**（1分钟）
   - 快速介绍30天四个阶段
   - 强调循序渐进的重要性

2. **个人承诺**（2分钟）
   - 学员选择3个优先应用场景
   - 现场制定具体行动计划
   - 相互分享和鼓励

### 课后支持说明（2分钟）

**支持内容**：
- 场景设计画布模板
- 微信学习群交流
- 20个行业场景模板参考
- 月度线上答疑会

**结束语**：
```
"90分钟的学习只是开始，真正的收获在于持续实践。希望大家把场景化思维带回企业，让AI成为真正的智能助手！"
```

---

## 时间控制策略

### 严格时间管理
- **总体原则**：宁少勿超，确保核心内容完整
- **缓冲设计**：预留5分钟机动时间
- **优先级**：震撼演示 > 现场PK > 理论讲解

### 各环节时间底线
- 第一部分：最少18分钟，最多22分钟
- 第二部分：最少35分钟，最多42分钟
- 第三部分：最少22分钟，最多27分钟
- 第四部分：最少3分钟，最多5分钟

### 应急预案
- **超时处理**：压缩理论讲解，保证实操时间
- **提前完成**：增加学员互动和案例分享
- **网络问题**：使用备份案例，不影响整体节奏

---

## 突发情况应对

### 技术故障
- **网络中断**：立即切换热点，使用备用演示
- **投影失效**：改用口述+白板，增加互动
- **AI平台故障**：使用预备截图案例

### 学员参与度问题
- **沉默应对**：主动点名，降低参与门槛
- **过度讨论**：巧妙引导，控制时间节奏
- **质疑抵触**：现场验证，用结果说话

### 讲师状态管理
- **保持高能量**：适时喝水，调整语调
- **观察反馈**：密切关注学员表情和参与度
- **灵活调整**：根据现场情况微调内容和节奏

---

## 成功要素检查清单

### 课前（开课前30分钟）
- [ ] 技术设备全部测试完毕
- [ ] 材料准备完整到位
- [ ] 环境布置温馨专业
- [ ] 个人状态积极饱满
- [ ] 时间计划熟记于心

### 课中（每个环节开始前）
- [ ] 检查时间进度
- [ ] 观察学员状态
- [ ] 调整教学节奏
- [ ] 确保互动质量

### 课后（结束后24小时内）
- [ ] 收集学员反馈
- [ ] 发送学习材料
- [ ] 建立跟踪机制
- [ ] 总结改进要点

**关键提醒**：90分钟虽然紧凑，但您的目标是在学员心中种下"场景化思维"的种子。重点不在于传授所有知识，而在于激发他们的学习兴趣和应用动机！ 